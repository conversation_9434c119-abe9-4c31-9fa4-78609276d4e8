# PowerShell script to fix Grid component issues in Material-UI v7
# This script will:
# 1. Remove Grid from imports where it's not needed
# 2. Replace Grid layouts with CSS Grid using Box components

$frontendPath = "frontend\src"
$files = Get-ChildItem -Path $frontendPath -Recurse -Filter "*.tsx" | Where-Object { $_.Name -notlike "*.test.*" }

Write-Host "Found $($files.Count) TypeScript React files to process"

foreach ($file in $files) {
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    $hasChanges = $false
    
    # Check if file contains Grid item usage
    if ($content -match "Grid.*item") {
        Write-Host "Processing: $($file.Name)"
        
        # Remove Grid from imports if it exists
        $content = $content -replace "Grid,\s*", ""
        $content = $content -replace ",\s*Grid", ""
        $content = $content -replace "{\s*Grid\s*}", "{}"
        
        # Replace common Grid patterns with Box + CSS Grid
        # Pattern 1: Grid container with spacing
        $content = $content -replace '<Grid container spacing=\{3\}>', '<Box sx={{ display: ''grid'', gridTemplateColumns: { xs: ''1fr'', sm: ''repeat(2, 1fr)'', md: ''repeat(4, 1fr)'' }, gap: 3 }}>'
        $content = $content -replace '<Grid container spacing=\{2\}>', '<Box sx={{ display: ''grid'', gridTemplateColumns: { xs: ''1fr'', sm: ''repeat(2, 1fr)'', md: ''repeat(3, 1fr)'' }, gap: 2 }}>'
        $content = $content -replace '<Grid container spacing=\{4\}>', '<Box sx={{ display: ''grid'', gridTemplateColumns: { xs: ''1fr'', sm: ''repeat(2, 1fr)'', md: ''repeat(4, 1fr)'' }, gap: 4 }}>'
        
        # Pattern 2: Grid items with different sizes
        $content = $content -replace '<Grid item xs=\{12\} sm=\{6\} md=\{3\}>', '<Box>'
        $content = $content -replace '<Grid item xs=\{12\} sm=\{6\} md=\{4\}>', '<Box>'
        $content = $content -replace '<Grid item xs=\{12\} md=\{6\}>', '<Box>'
        $content = $content -replace '<Grid item xs=\{12\} md=\{8\}>', '<Box>'
        $content = $content -replace '<Grid item xs=\{12\} md=\{4\}>', '<Box>'
        $content = $content -replace '<Grid item xs=\{12\}>', '<Box>'
        $content = $content -replace '<Grid item xs=\{6\}>', '<Box>'
        
        # Close Grid tags
        $content = $content -replace '</Grid>', '</Box>'
        
        if ($content -ne $originalContent) {
            $hasChanges = $true
            Set-Content -Path $file.FullName -Value $content -NoNewline
            Write-Host "  ✓ Updated $($file.Name)"
        }
    }
}

Write-Host "Grid component fixes completed!"
